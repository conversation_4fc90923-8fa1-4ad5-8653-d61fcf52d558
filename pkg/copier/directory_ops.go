package copier

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"

	"bella/pkg/progress"
	"github.com/ulikunitz/xz"
)

// mapCompressionLevel maps user-friendly names to gzip/xz constants.
func mapCompressionLevel(level string, compType string) (int, error) {
	var l int
	switch strings.ToLower(level) {
	case "good":
		l = 6
	case "better":
		l = 7
	case "best":
		l = 9
	default:
		l = 6
	}

	if compType == "tar.gz" {
		if l > gzip.BestCompression {
			l = gzip.BestCompression
		}
	} else if compType == "tar.xz" {
		// xz levels are 0-9, so this is fine.
	}
	return l, nil
}

// copyDirectory performs a sequential, recursive copy of a directory.
func copyDirectory(cfg *Config) error {
	log.Printf("Preparing to recursively copy directory %s to %s\n", cfg.Input, cfg.Output)

	type fileJob struct {
		srcPath  string
		destPath string
		size     int64
	}
	var fileJobs []fileJob
	var totalSize int64

	err := filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			return err
		}
		destPath := filepath.Join(cfg.Output, relPath)

		if d.IsDir() {
			if err := os.MkdirAll(destPath, 0755); err != nil {
				return err
			}
			if cfg.PreserveAttributes {
				if err := applyMetadata(path, destPath); err != nil {
					log.Printf("Warning: failed to apply metadata to directory '%s': %v", destPath, err)
				}
			}
			return nil
		}

		info, err := d.Info()
		if err != nil {
			return err
		}
		job := fileJob{srcPath: path, destPath: destPath, size: info.Size()}
		fileJobs = append(fileJobs, job)
		totalSize += info.Size()
		return nil
	})
	if err != nil {
		return fmt.Errorf("failed to scan source directory: %w", err)
	}

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("copying directory", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	var copiedSize int64
	for _, job := range fileJobs {
		fileCfg := *cfg
		fileCfg.Input = job.srcPath
		fileCfg.Output = job.destPath
		fileCfg.Progress = false
		fileCfg.isRecursive = true
		fileCfg.Append = false

		if err := copyFileSingleStage(&fileCfg); err != nil {
			return fmt.Errorf("failed to copy file %s: %w", job.srcPath, err)
		}

		copiedSize += job.size
		if cfg.Progress && reporter != nil {
			reporter.Update(copiedSize, totalSize)
		}
	}

	if cfg.Progress && reporter != nil {
		reporter.Update(totalSize, totalSize)
	}
	log.Println("Directory copy stage completed.")

	if cfg.Verify {
		log.Println("Starting directory verification stage.")
		if cfg.Progress && reporter != nil {
			reporter.SetStage("verifying directory")
			reporter.Update(0, totalSize)
		}

		var verifiedSize int64
		for _, job := range fileJobs {
			verifyCfg := *cfg
			verifyCfg.Input = job.srcPath
			verifyCfg.Output = job.destPath
			verifyCfg.Progress = false

			if err := doVerify(&verifyCfg); err != nil {
				return fmt.Errorf("verification failed for file '%s': %w", job.destPath, err)
			}
			verifiedSize += job.size
			if cfg.Progress && reporter != nil {
				reporter.Update(verifiedSize, totalSize)
			}
		}

		if cfg.Progress && reporter != nil {
			reporter.Finish(verifiedSize)
		}
		log.Println("Directory verification stage completed successfully.")
	} else {
		if cfg.Progress && reporter != nil {
			reporter.Finish(totalSize)
		}
	}

	return nil
}

// CreateArchive determines which archive format to use and creates it.
func CreateArchive(cfg *Config) error {
	switch cfg.CompressionType {
	case "tar.gz":
		return createTarGz(cfg)
	case "tar.xz":
		return createTarXz(cfg)
	case "zip":
		return createZip(cfg)
	default:
		return fmt.Errorf("unsupported archive type for directory compression: %s", cfg.CompressionType)
	}
}

// createTarGz creates a .tar.gz archive from a source directory.
func createTarGz(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	level, err := mapCompressionLevel(cfg.CompressionLevel, "tar.gz")
	if err != nil {
		return err
	}
	gzw, err := gzip.NewWriterLevel(outFile, level)
	if err != nil {
		return fmt.Errorf("failed to create gzip writer: %w", err)
	}
	defer gzw.Close()
	tw := tar.NewWriter(gzw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.gz)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		reporter.Finish(writtenSize)
	}
	return nil
}

// createTarXz creates a .tar.xz archive from a source directory.
func createTarXz(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	level, err := mapCompressionLevel(cfg.CompressionLevel, "tar.xz")
	if err != nil {
		return err
	}
	config := xz.WriterConfig{Level: level}
	xzw, err := config.NewWriter(outFile)
	if err != nil {
		return fmt.Errorf("failed to create xz writer: %w", err)
	}
	defer xzw.Close()
	tw := tar.NewWriter(xzw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.xz)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		reporter.Finish(writtenSize)
	}
	return nil
}

// createZip creates a .zip archive from a source directory.
func createZip(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()
	zw := zip.NewWriter(outFile)
	defer zw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (zip)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		header.Method = zip.Deflate
		writer, err := zw.CreateHeader(header)
		if err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(writer, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		reporter.Finish(writtenSize)
	}
	return nil
}
