package copier

import (
	"bufio"
	"compress/gzip"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync/atomic"
	"syscall"

	"bella/pkg/checksum"
	"bella/pkg/device"
	"bella/pkg/progress"
)

// copyFile is the central point for overwrite/append checks for single files.
func copyFile(cfg *Config) error {
	if cfg.ShouldUseMultiStage() {
		return copyFileMultiStage(cfg)
	}
	return copyFileSingleStage(cfg)
}

// copyFileMultiStage handles multi-stage copy operations (copy -> verify -> compress)
func copyFileMultiStage(cfg *Config) error {
	finalOutput := cfg.Output
	tempOutput := finalOutput + ".tmp.bella"
	failedOutput := finalOutput + ".FAILED"

	os.Remove(failedOutput)

	rawCfg := *cfg
	rawCfg.Compression = "none"
	rawCfg.Verify = false
	rawCfg.Output = tempOutput

	err := copyFileSingleStage(&rawCfg)
	if err != nil {
		os.Remove(tempOutput)
		return fmt.Errorf("stage 1 (copy) failed: %w", err)
	}

	if tempFile, err := os.OpenFile(tempOutput, os.O_WRONLY, 0); err == nil {
		tempFile.Sync()
		tempFile.Close()
	}

	if cfg.Verify {
		verifyCfg := *cfg
		verifyCfg.Operation = OpVerify
		verifyCfg.Input = cfg.Input
		verifyCfg.Output = tempOutput
		verifyCfg.Compression = "none"
		verifyCfg.Checksum = ""

		err = doVerify(&verifyCfg)
		if err != nil {
			os.Rename(tempOutput, failedOutput)
			return fmt.Errorf("stage 2 (verification) failed: %w. The incomplete file has been saved as %s", err, failedOutput)
		}
	}

	if cfg.Compression == "compress" {
		if cfg.Append {
			err = appendCompressedFile(tempOutput, finalOutput, cfg)
		} else {
			err = compressFile(tempOutput, finalOutput, cfg)
		}
		if err != nil {
			os.Rename(tempOutput, failedOutput)
			return fmt.Errorf("stage 3 (compression) failed: %w. The incomplete file has been saved as %s", err, failedOutput)
		}
		os.Remove(tempOutput)
	} else {
		if cfg.Append {
			err = appendTempFileToFinal(tempOutput, finalOutput)
		} else {
			err = os.Rename(tempOutput, finalOutput)
		}
		if err != nil {
			return fmt.Errorf("failed to finalize output file: %w", err)
		}
		if cfg.Append {
			os.Remove(tempOutput)
		}
	}

	if cfg.PreserveAttributes {
		if err := applyMetadata(cfg.Input, finalOutput); err != nil {
			log.Printf("Warning: failed to apply metadata to '%s': %v", finalOutput, err)
		}
	}

	return nil
}

// copyFileSingleStage handles single-stage copy operations
func copyFileSingleStage(cfg *Config) error {
	in, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open input '%s': %w", cfg.Input, err)
	}
	defer in.Close()

	if cfg.isRecursive {
		if err := os.MkdirAll(filepath.Dir(cfg.Output), 0755); err != nil {
			return fmt.Errorf("failed to create destination directory for '%s': %w", cfg.Output, err)
		}
	}

	openFlags := os.O_CREATE | os.O_WRONLY
	if cfg.Append {
		openFlags |= os.O_APPEND
	} else {
		openFlags |= os.O_TRUNC
	}
	out, err := createFileWithMetadata(cfg.Input, cfg.Output, openFlags, cfg.PreserveAttributes)
	if err != nil {
		return fmt.Errorf("failed to open output '%s': %w", cfg.Output, err)
	}
	defer out.Close()

	totalSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		log.Printf("Warning: could not determine input size: %v\n", err)
		totalSize = 0
	}

	if cfg.Count > 0 {
		countSize := int64(cfg.Count) * int64(cfg.BlockSize)
		if totalSize == 0 || countSize < totalSize {
			totalSize = countSize
		}
	}

	if cfg.ShouldUseCopyOffload() {
		reporter := progress.NewReporter("Kernel Offload", cfg.ProgressChan)
		if cfg.Progress {
			reporter.Update(0, totalSize)
		}

		progressCallback := func(written, total int64) {
			if cfg.Progress && reporter != nil {
				reporter.Update(written, total)
			}
		}

		written, err := device.CopyFileRangeWithProgress(in, out, totalSize, progressCallback)
		if err == nil {
			if cfg.Progress {
				reporter.Finish(written)
			}
			return nil
		}
		if err != syscall.ENOSYS {
			return fmt.Errorf("kernel copy failed: %w", err)
		}
		log.Println("Kernel offload not supported, falling back to standard copy.")
	}

	if cfg.Skip > 0 {
		if _, err := in.Seek(cfg.Skip*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to skip in input: %w", err)
		}
	}
	if cfg.Seek > 0 && !cfg.Append {
		if _, err := out.Seek(cfg.Seek*int64(cfg.BlockSize), io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek in output: %w", err)
		}
	}

	var reader io.Reader = in
	if cfg.Compression == "decompress" || (cfg.Compression == "auto" && strings.HasSuffix(cfg.Input, ".gz")) {
		if cfg.CompressionType == "gzip" || strings.HasSuffix(cfg.Input, ".gz") {
			gr, err := gzip.NewReader(reader)
			if err != nil {
				return fmt.Errorf("failed to create gzip reader: %w", err)
			}
			defer gr.Close()
			reader = gr
			totalSize = 0
		}
	}

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Copying", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	bufReader := bufio.NewReaderSize(reader, cfg.BlockSize)
	err = copyLoop(bufReader, out, out, cfg, nil, totalSize, reporter)
	if err != nil {
		return err
	}
	if reporter != nil && !cfg.isRecursive {
		reporter.Finish(totalSize)
	}

	return nil
}

// copyLoop is the heart of the copy process.
func copyLoop(r io.Reader, w io.Writer, outFile *os.File, cfg *Config, copiedSize *int64, totalSize int64, reporter *progress.Reporter) error {
	buf := make([]byte, cfg.BlockSize)
	blocksCopied := 0

	var hasher *checksum.Hasher
	if cfg.Checksum != "" {
		var err error
		hasher, err = checksum.NewHasher(cfg.Checksum)
		if err != nil {
			return fmt.Errorf("failed to initialize checksum: %w", err)
		}
	}

	if copiedSize == nil {
		var singleFileSize int64
		copiedSize = &singleFileSize
	}

	for {
		if cfg.Count > 0 && blocksCopied >= cfg.Count {
			break
		}

		n, err := r.Read(buf)
		if n > 0 {
			if hasher != nil {
				if _, hashErr := hasher.Write(buf[:n]); hashErr != nil {
					return fmt.Errorf("checksum calculation error: %w", hashErr)
				}
			}

			if cfg.Sparse && isAllZeros(buf[:n]) {
				if _, seekErr := outFile.Seek(int64(n), io.SeekCurrent); seekErr != nil {
					if _, wErr := w.Write(buf[:n]); wErr != nil {
						return fmt.Errorf("sparse seek and fallback write failed: %w", wErr)
					}
				}
			} else {
				if _, wErr := w.Write(buf[:n]); wErr != nil {
					return fmt.Errorf("write error: %w", wErr)
				}
			}

			atomic.AddInt64(copiedSize, int64(n))
			if reporter != nil && !cfg.isRecursive {
				reporter.Update(atomic.LoadInt64(copiedSize), totalSize)
			}

			blocksCopied++
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			if cfg.SkipBadSectors {
				log.Printf("Read error, skipping: %v\n", err)
				zeroBuf := make([]byte, cfg.BlockSize)
				if _, wErr := w.Write(zeroBuf); wErr != nil {
					return fmt.Errorf("error writing zero block for bad sector: %w", wErr)
				}
				atomic.AddInt64(copiedSize, int64(cfg.BlockSize))
				if reporter != nil && !cfg.isRecursive {
					reporter.Update(atomic.LoadInt64(copiedSize), totalSize)
				}
				continue
			}
			return fmt.Errorf("read error: %w", err)
		}
	}

	if outFile != nil {
		outFile.Sync()
	}

	if f, ok := w.(io.Closer); ok {
		f.Close()
	}

	if hasher != nil {
		checksumValue := fmt.Sprintf("%x", hasher.Sum(nil))
		if !cfg.isRecursive {
			checksumFilename := cfg.Output + "." + strings.ToLower(string(hasher.Algorithm))
			content := fmt.Sprintf("%s  %s\n", checksumValue, filepath.Base(cfg.Output))
			if err := os.WriteFile(checksumFilename, []byte(content), 0664); err != nil {
				log.Printf("Warning: Failed to write checksum file: %v", err)
			}
			fmt.Printf("Checksum file created: %s\n", checksumFilename)
		} else {
			log.Printf("Checksum (%s) for TAR stream: %s", string(hasher.Algorithm), checksumValue)
		}
	}

	return nil
}
